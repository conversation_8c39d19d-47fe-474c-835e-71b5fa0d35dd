# Escape Room Game System Documentation

## Table of Contents

1. [Hardware Setup](#1-hardware-setup)
   - [Team A Components](#team-a-code-generator--weight-validator)
   - [Team B Components](#team-b-code-input)
2. [Software Architecture](#2-software-architecture)
   - [Network Infrastructure](#a-network-infrastructure)
   - [Game States](#b-game-states)
3. [Game Flow](#3-game-flow)
   - [Initialization Phase](#a-initialization-phase)
   - [Ready Phase](#b-ready-phase)
   - [Stage 1 - Code Challenge](#c-stage-1---code-challenge)
   - [Stage 2 - Weight Challenge](#d-stage-2---weight-challenge)
4. [User Interface Elements](#4-user-interface-elements)
   - [LCD Displays](#a-lcd-displays)
   - [Web Interfaces](#b-web-interfaces)
5. [Error Handling and Reliability](#5-error-handling-and-reliability)
6. [Game Configuration](#6-game-configuration)
7. [MQTT Communication](#7-mqtt-communication)

## 1. Hardware Setup

### Team A (Code Generator & Weight Validator)

#### Core Components

- **ESP32 Microcontroller**
  - Primary game logic controller
  - WiFi connectivity to "FRITZ!Box 6660 Cable LE"
  - Unique MQTT client ID based on machine ID

#### Pin Configuration

```python
TEAM_A_PINS = {
    'START_BUTTON': 2,      # GPIO2 - Multi-function button
    'STATUS_LED': 4,        # GPIO4
    'BUZZER': 5,            # GPIO5
    'PRESSURE_SENSOR': 3,   # GPIO3 (ADC)
    'LCD_SDA': 11,         # GPIO11
    'LCD_SCL': 12,         # GPIO12
}
```

#### Hardware Components Details

1. **20x4 LCD Display (I2C)**

   - Real-time game status display
   - Generated code visualization
   - Weight readings display
   - Game state information

2. **Pressure Sensor**

   - Calibrated range: 0-10kg
   - Moving average filter implementation
   - 50g tolerance for target matching
   - Regular calibration checks

3. **Status LED & Buzzer**
   - Game state indication patterns
   - Event-based sound effects
   - Visual feedback for actions

### Team B (Code Input)

#### Core Components

- **ESP32 Microcontroller**
  - Code input processing
  - Display management
  - WiFi connectivity

#### Pin Configuration

```python
TEAM_B_PINS = {
    'CONFIRM_BUTTON': 13,           # GPIO13
    'STATUS_LED': 14,               # GPIO14
    'BUZZER': 7,                    # GPIO7
    'ROTARY_SWITCH_HUNDREDS': 6,    # GPIO6 (CLK)
    'ROTARY_SWITCH_TENS': 8,        # GPIO8 (CLK)
    'ROTARY_SWITCH_ONES': 9,        # GPIO9 (CLK)
    'LCD_SDA': 47,                  # GPIO47
    'LCD_SCL': 38,                  # GPIO38
}
```

#### Hardware Components Details

1. **Rotary Encoders (3x)**

   - Individual digit control (hundreds/tens/ones)
   - Hardware debouncing
   - Directional detection (CLK/DT pairs)
   - Smooth rotation handling

2. **20x4 LCD Display**

   - Current code input display
   - Target weight visualization
   - Game state information
   - Attempt counter

3. **Interactive Components**
   - Confirmation button
   - Status LED for visual feedback
   - Buzzer for audio feedback

## 2. Software Architecture

### A. Network Infrastructure

#### 1. MQTT Broker

- Host: **************
- Port: 1883
- Internal network operation
- No authentication required

#### 2. Node-RED Server

- Web interface hosting
- Multiple dashboard views:
  - Team A dashboard (code display)
  - Team B dashboard (communication)
  - Admin dashboard (full overview)

#### 3. Network Manager Implementation

```python
class NetworkManager:
    def __init__(self, client_id, broker, port=1883):
        self.connection_retry_delay = 1  # Exponential backoff
        self.max_retry_delay = 30
        self.subscribed_topics = []
```

Key Features:

- Automatic MQTT connection management
- Exponential backoff for reconnection
- Topic subscription handling
- Error recovery system

### B. Game States

```
waiting → ready → stage1 → stage2 → completed
    ↑                              ↓
    ←------------ reset ←-----------
                    ↓
                 failed (max attempts reached)
```

## 3. Game Flow

### A. Initialization Phase

#### 1. Hardware Initialization

```python
def init_hardware(self):
    # Team A
    self.pressure_sensor = PressureSensor(TEAM_A_PINS['PRESSURE_SENSOR'])
    self.pressure_sensor.calibrate_zero()

    # Team B
    self.code_entry = CodeEntry(
        TEAM_B_PINS['ROTARY_SWITCH_HUNDREDS'],
        TEAM_B_PINS['ROTARY_SWITCH_TENS'],
        TEAM_B_PINS['ROTARY_SWITCH_ONES'],
        TEAM_B_PINS['CONFIRM_BUTTON']
    )
```

#### 2. Network Setup

- WiFi connection establishment
- MQTT broker connection
- Topic subscription initialization

### B. Ready Phase

#### 1. Team Ready State Management

```json
{
  "team": "A/B",
  "ready": true,
  "timestamp": 1234567890
}
```

#### 2. Game Start Sequence

- Both teams ready confirmation
- 3-second countdown implementation
- State transition to stage1

### C. Stage 1 - Code Challenge

#### 1. Code Generation (Team A)

```python
def start_game(self):
    self.current_code = random.randint(100, 999)
    self.game_state = "stage1"

    code_data = {
        "code": self.current_code,
        "timestamp": int(time.time() * 1000)
    }
    self.mqtt_client.publish(b"escape_room/stage1/code_generated",
                           json.dumps(code_data))
```

#### 2. Code Communication System

- Team A code visibility
- 2-character message system
- Web interface communication

#### 3. Code Input System (Team B)

```python
def check_code_input(self):
    result = self.code_entry.update()
    if result['confirmed']:
        attempted_code = result['confirmed_code']
        self.attempts_remaining -= 1

        attempt_data = {
            "code": attempted_code,
            "attempt": 6 - self.attempts_remaining,
            "attempts_remaining": self.attempts_remaining,
            "timestamp": time.time()
        }
        self.mqtt_client.publish(b"escape_room/team_b/code_attempt",
                               json.dumps(attempt_data))
```

#### 4. Verification Process

```python
def handle_code_attempt(self, data):
    attempted_code = data.get("code")
    if attempted_code == self.current_code:
        self.stage1_completed = True
        self.game_state = "stage2"
        # Generate weight for stage 2
        self.target_weight = random.randint(4, 40) * 250
```

### D. Stage 2 - Weight Challenge

#### 1. Weight Generation

```python
# Weight generation (1-10kg in 250g increments)
raw_weight = random.randint(4, 40)  # 4-40 * 250g = 1000g-10000g
self.target_weight = raw_weight * 250
```

#### 2. Weight Monitoring System

```python
def check_pressure_sensor(self):
    current_weight = self.pressure_sensor.read_weight()
    if abs(current_weight - self.target_weight) <= 50:  # 50g tolerance
        if self.weight_in_range_start == 0:
            self.weight_in_range_start = time.time()
        elif time.time() - self.weight_in_range_start >= 1:
            self.handle_stage2_completed()
```

## 4. User Interface Elements

### A. LCD Displays

#### Team A Display Features

- Game state visualization
- Generated code display
- Weight monitoring
- Target weight display

#### Team B Display Features

- Game state information
- Code input visualization
- Attempt counter
- Target weight display

### B. Web Interfaces

#### 1. Team A Dashboard

- Code display section
- Message input system
- Game state monitor
- Weight status display

#### 2. Team B Dashboard

- Message display section
- Game state visualization
- Target weight display
- Attempt counter

#### 3. Admin Dashboard

- Complete game overview
- Team status monitoring
- Communication log
- Game control interface

## 5. Error Handling and Reliability

### 1. Network Resilience

- MQTT reconnection system
- Exponential backoff implementation
- State synchronization mechanism

### 2. Input Validation

```python
def validate_code(code):
    if not isinstance(code, int):
        return False, "Code must be a number"
    if not (100 <= code <= 999):
        return False, "Code must be between 100 and 999"
    return True, "Valid code"
```

### 3. Hardware Monitoring

- Component health checking
- Sensor calibration system
- Error detection and reporting

## 6. Game Configuration

```python
GAME_CONFIG = {
    'max_attempts': 5,
    'weight_tolerance': 50,  # grams
    'min_target_weight': 1000,  # grams (1kg)
    'max_target_weight': 10000,  # grams (10kg)
    'status_update_interval': 2,  # seconds
    'timeout_minutes': 30,  # game timeout
}
```

## 7. MQTT Communication

### Topic Structure

```
escape_room/
├── game_control/         # Game control commands
├── team_a/
│   ├── ready            # Team A ready status
│   └── code             # Generated code (to Node-RED only)
├── team_b/
│   ├── ready            # Team B ready status
│   └── code_attempt     # Code attempts
├── stage1/
│   └── code_generated   # Code generation notification
└── stage2/
    └── target_weight    # Target weight for stage 2
```

### Message Examples

#### Ready State

```json
{
  "team": "A/B",
  "ready": true,
  "timestamp": 1234567890
}
```

#### Code Generation

```json
{
  "code": 456,
  "timestamp": 1234567890
}
```

#### Code Attempt

```json
{
  "code": 456,
  "attempt": 1,
  "attempts_remaining": 4,
  "timestamp": 1234567890
}
```

#### Weight Challenge

```json
{
  "stage": 2,
  "target_weight": 5000,
  "timestamp": 1234567890
}
```

#### Status Updates

```json
{
  "team": "A",
  "game_state": "stage2",
  "ready": true,
  "current_weight": 4950,
  "target_weight": 5000,
  "stage1_completed": true,
  "stage2_completed": false,
  "timestamp": 1234567890
}
```

---

This documentation provides a comprehensive overview of the escape room game system, including hardware setup, software architecture, game flow, user interfaces, and communication protocols. The system is designed to create an engaging escape room experience that requires teamwork and effective communication between teams to solve the challenges.
