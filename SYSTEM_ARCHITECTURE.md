# Escape Room System Architecture

## Overview

This document provides a comprehensive analysis of the escape room game system architecture. The system is built on ESP32 microcontrollers running MicroPython and features a two-team collaborative puzzle-solving experience with strict communication constraints.

## System Summary

- **Platform**: ESP32 microcontrollers with MicroPython
- **Teams**: Two teams (A and B) with different hardware setups
- **Communication**: MQTT-based messaging with 2-character text limitations
- **Game Stages**: 
  1. Code Challenge (Team A displays → Team B enters)
  2. Weight Challenge (Team B displays → Team A applies pressure)
- **Admin Interface**: Node-RED dashboard for monitoring and messaging

## Comprehensive System Architecture Diagram

```mermaid
graph TD
    %% ESP32 Hardware Components
    subgraph ESP32_A ["🎮 Team A ESP32"]
        A_LCD["📺 LCD Display<br/>SDA:11, SCL:12<br/>Shows: Code, Status"]
        A_BTN["🔘 Start Button<br/>GPIO2<br/>Multi-function"]
        A_LED["💡 Status LED<br/>GPIO4<br/>Patterns"]
        A_BUZ["🔊 Buzzer<br/>GPIO5<br/>Audio feedback"]
        A_PRESS["⚖️ Pressure Sensor<br/>GPIO3 ADC<br/>Weight detection"]
    end

    subgraph ESP32_B ["🎮 Team B ESP32"]
        B_LCD["📺 LCD Display<br/>SDA:47, SCL:38<br/>Shows: Weight, Status"]
        B_BTN["🔘 Confirm Button<br/>GPIO13<br/>Manual confirm"]
        B_LED["💡 Status LED<br/>GPIO14<br/>Patterns"]
        B_BUZ["🔊 Buzzer<br/>GPIO7<br/>Audio feedback"]
        B_ROT1["🔄 Hundreds Switch<br/>CLK:6, DT:17<br/>0-9 digits"]
        B_ROT2["🔄 Tens Switch<br/>CLK:8, DT:18<br/>0-9 digits"]
        B_ROT3["🔄 Ones Switch<br/>CLK:9, DT:21<br/>0-9 digits"]
    end

    %% Software Layer
    subgraph SW_A ["💻 Team A Controller"]
        A_CTRL["TeamAController<br/>• Game state host<br/>• Pressure monitoring<br/>• Code generation"]
        A_MQTT["MQTT Manager<br/>• Connection handling<br/>• Message queuing<br/>• Auto-reconnect"]
        A_GAME["GameState<br/>• Stage progression<br/>• Score calculation<br/>• Timer management"]
    end

    subgraph SW_B ["💻 Team B Controller"]
        B_CTRL["TeamBController<br/>• Code entry<br/>• Weight display<br/>• Switch monitoring"]
        B_MQTT["MQTT Manager<br/>• Connection handling<br/>• Message queuing<br/>• Auto-reconnect"]
        B_CODE["CodeEntry System<br/>• 3-digit input<br/>• Manual confirmation<br/>• Debouncing"]
    end

    %% Communication Infrastructure
    subgraph COMM ["🌐 Communication Layer"]
        WIFI["📡 WiFi Network<br/>SSID: G3011_WLAN<br/>Auto-reconnect"]
        BROKER["🔗 MQTT Broker<br/>IP: *************<br/>Port: 1883"]
        TOPICS["📋 MQTT Topics<br/>• game/{id}/control/*<br/>• game/{id}/stage1/*<br/>• game/{id}/stage2/*<br/>• game/{id}/communication/*"]
    end

    %% Admin Interface
    subgraph ADMIN ["⚙️ Admin Interface"]
        NODE_RED["🎛️ Node-RED Dashboard<br/>Port: 1880<br/>Admin endpoint: /admin"]
        MSG_SYS["💬 2-Character Messaging<br/>• Rate limited<br/>• Message history<br/>• A↔B communication"]
        MONITOR["📊 Game Monitoring<br/>• Stage progress<br/>• Team status<br/>• Performance metrics"]
    end

    %% Game Flow
    subgraph GAME_FLOW ["🎯 Game Progression"]
        INIT["🚀 Initialization<br/>• WiFi connect<br/>• MQTT connect<br/>• Hardware setup"]
        CALIB["📏 Calibration<br/>• Pressure sensor zero<br/>• Switch positioning<br/>• LCD test"]
        READY["✅ Ready State<br/>• Both teams ready<br/>• Systems validated<br/>• Game can start"]
        STAGE1["🔢 Stage 1: Code Challenge<br/>• Team A shows code<br/>• Team B enters code<br/>• Max 5 attempts"]
        STAGE2["⚖️ Stage 2: Weight Challenge<br/>• Team B shows weight<br/>• Team A applies pressure<br/>• Tolerance: ±100g"]
        COMPLETE["🏆 Game Complete<br/>• Score calculation<br/>• Performance stats<br/>• Victory celebration"]
    end

    %% Hardware to Software Connections
    A_LCD -.-> A_CTRL
    A_BTN -.-> A_CTRL
    A_LED -.-> A_CTRL
    A_BUZ -.-> A_CTRL
    A_PRESS -.-> A_CTRL

    B_LCD -.-> B_CTRL
    B_BTN -.-> B_CTRL
    B_LED -.-> B_CTRL
    B_BUZ -.-> B_CTRL
    B_ROT1 -.-> B_CODE
    B_ROT2 -.-> B_CODE
    B_ROT3 -.-> B_CODE
    B_CODE -.-> B_CTRL

    %% Software Layer Connections
    A_CTRL <--> A_MQTT
    A_CTRL <--> A_GAME
    B_CTRL <--> B_MQTT

    %% Network Connections
    A_MQTT <--> WIFI
    B_MQTT <--> WIFI
    WIFI <--> BROKER
    BROKER <--> TOPICS

    %% Admin Connections
    BROKER <--> NODE_RED
    NODE_RED <--> MSG_SYS
    NODE_RED <--> MONITOR

    %% Game Flow Connections
    INIT --> CALIB
    CALIB --> READY
    READY --> STAGE1
    STAGE1 --> STAGE2
    STAGE2 --> COMPLETE

    %% Data Flow Messages
    A_GAME -->|"stage1_code<br/>{code: 123}"| TOPICS
    TOPICS -->|"stage1_code"| B_CTRL
    B_CTRL -->|"📺 Display: Enter Code"| B_LCD

    B_CODE -->|"stage1_attempt<br/>{code: 123, team: B}"| TOPICS
    TOPICS -->|"stage1_attempt"| A_CTRL
    A_CTRL -->|"stage1_result<br/>{success: true/false}"| TOPICS

    A_CTRL -->|"stage2_target<br/>{weight: 5000g}"| TOPICS
    TOPICS -->|"stage2_target"| B_CTRL
    B_CTRL -->|"📺 Display: 5.0 KG"| B_LCD

    A_PRESS -->|"Weight readings"| A_CTRL
    A_CTRL -->|"stage2_current<br/>{weight: 4950g}"| TOPICS

    %% Admin Dashboard Communication
    MSG_SYS <-->|"message_to_a<br/>message_to_b"| TOPICS
    TOPICS <-->|"2-char messages"| A_CTRL
    TOPICS <-->|"2-char messages"| B_CTRL

    %% Status and Monitoring
    A_CTRL -->|"status_team_a<br/>heartbeat"| TOPICS
    B_CTRL -->|"status_team_b<br/>heartbeat"| TOPICS
    TOPICS -->|"Game status"| MONITOR

    %% Styling
    classDef hardware fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef software fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef communication fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef admin fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef gameflow fill:#fce4ec,stroke:#880e4f,stroke-width:2px

    class ESP32_A,ESP32_B,A_LCD,A_BTN,A_LED,A_BUZ,A_PRESS,B_LCD,B_BTN,B_LED,B_BUZ,B_ROT1,B_ROT2,B_ROT3 hardware
    class SW_A,SW_B,A_CTRL,A_MQTT,A_GAME,B_CTRL,B_MQTT,B_CODE software
    class COMM,WIFI,BROKER,TOPICS communication
    class ADMIN,NODE_RED,MSG_SYS,MONITOR admin
    class GAME_FLOW,INIT,CALIB,READY,STAGE1,STAGE2,COMPLETE gameflow
```

## Hardware Components

### Team A Hardware
- **ESP32 Controller**: Main processing unit
- **LCD Display**: 16x2 I2C display (SDA: GPIO11, SCL: GPIO12, Address: 0x27)
- **Start Button**: Multi-function button on GPIO2
- **Status LED**: Visual feedback on GPIO4 with multiple patterns
- **Buzzer**: Audio feedback on GPIO5 with musical capabilities
- **Pressure Sensor**: ADC-based weight measurement on GPIO3

### Team B Hardware
- **ESP32 Controller**: Main processing unit
- **LCD Display**: 16x2 I2C display (SDA: GPIO47, SCL: GPIO38, Address: 0x27)
- **Confirm Button**: Manual confirmation button on GPIO13
- **Status LED**: Visual feedback on GPIO14 with multiple patterns
- **Buzzer**: Audio feedback on GPIO7 with musical capabilities
- **Rotary Encoders**: Three switches for 3-digit code entry
  - Hundreds: CLK=GPIO6, DT=GPIO17
  - Tens: CLK=GPIO8, DT=GPIO18
  - Ones: CLK=GPIO9, DT=GPIO21

## Software Architecture

### Core Modules

#### Team Controllers
- **TeamAController**: Manages game state, pressure monitoring, code generation
- **TeamBController**: Handles code entry, weight display, switch monitoring

#### Hardware Abstraction Layer
- **GameLCD**: I2C LCD display management with game-specific methods
- **GameButton**: Advanced button handling with debouncing and event detection
- **StatusLED**: LED pattern management with threading support
- **GameBuzzer**: Musical buzzer with predefined sound patterns
- **PressureSensor**: ADC-based weight measurement with calibration
- **CodeEntry**: 3-digit rotary encoder input system with manual confirmation

#### Communication System
- **EnhancedMQTTManager**: Robust MQTT client with auto-reconnection and message queuing
- **GameMessageHandler**: Message routing and processing with communication features

#### Game Logic
- **GameState**: Centralized game state management with scoring and timing
- **Configuration System**: Hierarchical configuration with templates and overrides

### Communication Protocol

#### MQTT Topic Structure
```
game/{game_id}/
├── control/
│   ├── ready          # Team readiness
│   ├── start          # Game start signal
│   ├── reset          # Game reset
│   └── admin          # Admin commands
├── stage1/
│   ├── code_generated # Code display
│   ├── code_attempt   # Code submission
│   ├── attempt_result # Success/failure feedback
│   └── complete       # Stage completion
├── stage2/
│   ├── weight_target  # Target weight display
│   ├── weight_current # Current weight updates
│   └── complete       # Stage completion
├── communication/
│   ├── message/A      # Messages to Team A
│   ├── message/B      # Messages to Team B
│   └── history        # Message history
└── status/
    ├── team_a         # Team A status
    ├── team_b         # Team B status
    └── game           # Overall game status
```

#### Message Types
- **Control Messages**: Game state control (ready, start, reset)
- **Stage Messages**: Stage-specific data (codes, weights, results)
- **Communication Messages**: 2-character inter-team messaging
- **Status Messages**: System health and performance data
- **Admin Messages**: Remote control and monitoring commands

## Game Flow

### 1. Initialization Phase
- WiFi connection with retry logic
- MQTT broker connection
- Hardware component initialization
- LCD display testing
- System status verification

### 2. Calibration Phase
- **Team A**: Pressure sensor zero calibration
- **Team B**: Rotary switch positioning
- LCD feedback during calibration
- Status reporting to admin dashboard

### 3. Ready Phase
- Teams signal readiness via button presses
- System validation checks
- Cross-team communication testing
- Game start authorization

### 4. Stage 1: Code Challenge
- **Team A**: 
  - Generates random 3-digit code (100-999)
  - Displays code on LCD immediately
  - Monitors code attempts from Team B
  - Provides success/failure feedback
- **Team B**:
  - Receives game start notification
  - Uses rotary encoders to set 3-digit code
  - Manual confirmation via button press
  - Receives attempt results with LCD feedback

### 5. Stage 2: Weight Challenge
- **Team B**:
  - Generates random target weight (1-10kg)
  - Displays weight on LCD immediately
  - Monitors weight progress from Team A
- **Team A**:
  - Receives target weight notification
  - Applies pressure to sensor
  - Real-time weight monitoring with tolerance
  - Success when within ±100g tolerance

### 6. Completion Phase
- Game completion celebration
- Final score calculation
- Performance statistics
- System reset for next game

## Communication Features

### 2-Character Messaging System
- **Rate Limited**: Minimum 2 seconds between messages
- **Length Restricted**: Maximum 2 characters per message
- **Character Set**: A-Z, 0-9 only
- **History Tracking**: Last 50 messages stored
- **Admin Interface**: Node-RED dashboard for message management

### Message Flow Examples
```
Team A → Admin Dashboard → MQTT → Team B
"HI" → message_to_b → "Message from A: HI"

Team B → Admin Dashboard → MQTT → Team A  
"OK" → message_to_a → "Message from B: OK"
```

## Configuration System

### Hierarchical Configuration
- **config_template.py**: Template with all options and documentation
- **config/network_config.py**: WiFi and MQTT settings
- **config/hardware_config.py**: Pin assignments and hardware parameters
- **config/game_config.py**: Game rules and difficulty settings

### Key Configuration Categories
- **Network Settings**: WiFi credentials, MQTT broker details
- **Hardware Pins**: GPIO assignments for all components
- **Game Parameters**: Difficulty levels, timeouts, scoring
- **Communication**: Message limits, rate limiting, history size
- **Display Settings**: LCD timing, patterns, feedback duration

## Error Handling and Reliability

### Connection Management
- **Auto-Reconnection**: Both WiFi and MQTT with exponential backoff
- **Message Queuing**: Store messages during disconnection
- **Heartbeat Monitoring**: Regular status updates for health checking
- **Graceful Degradation**: Continue operation with reduced functionality

### Hardware Reliability
- **Debouncing**: All inputs properly debounced (300ms default)
- **Sensor Calibration**: Automatic zero-point calibration
- **Error Recovery**: Automatic retry mechanisms
- **Emergency Reset**: Watchdog-style system restart on fatal errors

### Game State Protection
- **Atomic Operations**: Critical state changes are atomic
- **Rollback Capability**: Can revert to previous stable state
- **Validation**: Input validation at all entry points
- **Logging**: Comprehensive logging for debugging

## Performance Characteristics

### Timing Requirements
- **LCD Updates**: Instant feedback (<100ms)
- **Button Response**: Immediate debounced response
- **MQTT Latency**: <500ms typical network latency
- **Sensor Readings**: 5Hz update rate for pressure sensor
- **Game Loop**: 10Hz main loop frequency

### Resource Management
- **Memory**: Garbage collection in main loops
- **CPU**: Non-blocking operations with threading
- **Network**: Connection pooling and efficient message handling
- **Storage**: Minimal persistent storage requirements

## Security Considerations

### Network Security
- **Open WiFi**: Currently uses open network (could be enhanced)
- **MQTT**: No authentication (suitable for isolated network)
- **Message Validation**: All inputs validated before processing

### Game Integrity
- **Centralized State**: Team A hosts authoritative game state
- **Input Validation**: All code/weight inputs validated
- **Anti-Cheating**: Manual confirmation required for submissions
- **Audit Trail**: Complete game history logged

## Deployment and Operations

### Installation Process
1. Flash MicroPython to ESP32 controllers
2. Copy project files using ampy
3. Configure network settings
4. Set up MQTT broker
5. Deploy Node-RED dashboard
6. Test hardware connections
7. Calibrate sensors

### Monitoring and Maintenance
- **Admin Dashboard**: Real-time system monitoring
- **Health Checks**: Automated system status reporting
- **Performance Metrics**: Game timing and success statistics
- **Error Logging**: Centralized error collection and analysis

### Troubleshooting
- **LED Patterns**: Visual system status indicators
- **LCD Messages**: Error messages displayed locally
- **MQTT Debugging**: Network traffic monitoring
- **Log Analysis**: Detailed logging for problem diagnosis

## Future Enhancement Opportunities

### Hardware Enhancements
- **Security**: Add authentication to MQTT broker
- **Sensors**: Additional sensor types for new challenges
- **Displays**: Larger displays or additional visual feedback
- **Audio**: Enhanced audio feedback with music

### Software Improvements
- **Web Interface**: Browser-based admin interface
- **Database**: Persistent game statistics
- **Analytics**: Advanced performance analysis
- **Mobile App**: Smartphone integration for teams

### Game Features
- **Multiple Stages**: More than 2 challenge stages
- **Dynamic Difficulty**: Adaptive difficulty based on performance
- **Team Scoring**: Competitive scoring between multiple teams
- **Remote Play**: Internet-based distributed gameplay

---

*This document was automatically generated by analyzing the complete escape room codebase. Last updated: 2025-06-24*