# MQTT Data Flow Analysis - ESP32 Communication

## Overview
This document explains the MQTT communication architecture between Team A and Team B ESP32 controllers in the escape room game system.

## MQTT Architecture

### Broker Configuration
- **Broker Address**: `*************:1883`
- **QoS Level**: 1 (reliable delivery)
- **Keep Alive**: 60 seconds
- **Auto-reconnection**: Enabled with exponential backoff

### Client Identification
- **Team A**: `team_a_escape_room_2024_A_{timestamp}`
- **Team B**: `team_b_escape_room_2024_B_{timestamp}`

## Topic Structure

All topics follow the pattern: `game/{game_id}/category/subcategory`

### Control Topics
```
game/escape_room_2024/control/
├── ready          # Team readiness signals
├── start          # Game start command
├── reset          # Game reset command
└── admin          # Admin commands
```

### Stage 1 Topics (Code Challenge)
```
game/escape_room_2024/stage1/
├── code_generated # Team A → Team B (code display)
├── code_attempt   # Team B → Team A (code submission)
├── attempt_result # Team A → Team B (success/failure)
└── complete       # Stage completion notification
```

### Stage 2 Topics (Weight Challenge)
```
game/escape_room_2024/stage2/
├── weight_target  # Team B → Team A (target weight)
├── weight_current # Team A → Team B (current weight)
└── complete       # Stage completion notification
```

### Communication Topics
```
game/escape_room_2024/communication/
├── message/A      # Messages to Team A
├── message/B      # Messages to Team B
└── history        # Message history for dashboards
```

### Status & Monitoring Topics
```
game/escape_room_2024/
├── status/team_a     # Team A status reports
├── status/team_b     # Team B status reports
├── status/game       # Overall game status
└── heartbeat/{team}  # Connection health monitoring
```

## Message Flow Patterns

### 1. Game Initialization Flow
```
Team A → ready: {"team": "A", "ready": true, "calibrated": true}
Team B → ready: {"team": "B", "ready": true, "switches_zeroed": true}
Team A → start: {"game_id": "escape_room_2024", "timestamp": **********}
```

### 2. Stage 1 Flow (Code Challenge)
```
Team A generates code internally
Team A → stage1_code: {"code": 456, "stage": 1, "timestamp": **********}
Team B receives code and displays "Enter Code"
Team B → stage1_attempt: {"code": 456, "team": "B", "attempt_number": 1}
Team A → stage1_result: {"success": true, "code": 456, "timestamp": **********}
Team B → stage1_complete: {"complete": true, "team": "B"}
```

### 3. Stage 2 Flow (Weight Challenge)
```
Team B generates target weight internally
Team B → stage2_target: {"target_weight": 5000, "stage": 2, "team": "B"}
Team A applies pressure
Team A → stage2_current: {"weight": 4950, "target": 5000, "progress": 99.0}
Team A → stage2_complete: {"complete": true, "final_weight": 4950}
```

## Message Formats

### Standard Message Structure
All messages include automatic metadata:
```json
{
  "timestamp": **********.123,
  "source": "A",
  // ... message-specific data
}
```

### Control Messages
```json
// Ready Signal
{
  "team": "A",
  "ready": true,
  "calibrated": true,
  "timestamp": **********.123,
  "source": "A"
}

// Game Start
{
  "game_id": "escape_room_2024",
  "timestamp": **********.123,
  "source": "A"
}
```

### Stage 1 Messages
```json
// Code Generated (Team A → Team B)
{
  "code": 456,
  "stage": 1,
  "timestamp": **********.123,
  "source": "A"
}

// Code Attempt (Team B → Team A)
{
  "code": 456,
  "team": "B",
  "attempt_number": 1,
  "timestamp": 1719264015.123,
  "source": "B"
}

// Attempt Result (Team A → Team B)
{
  "success": false,
  "code": 456,
  "correct_code": 789,
  "attempts_remaining": 3,
  "timestamp": 1719264016.123,
  "source": "A"
}
```

### Stage 2 Messages
```json
// Weight Target (Team B → Team A)
{
  "target_weight": 5000,
  "stage": 2,
  "team": "B",
  "timestamp": **********.123,
  "source": "B"
}

// Current Weight (Team A → Team B)
{
  "weight": 4950,
  "target": 5000,
  "progress": 99.0,
  "timestamp": 1719264025.123,
  "source": "A"
}
```

### Communication Messages
```json
// Inter-team Communication (2-character limit)
{
  "message": "HI",
  "from_team": "B",
  "to_team": "A",
  "timestamp": 1719264030.123,
  "source": "B"
}
```

### Heartbeat Messages
```json
{
  "status": "online",
  "uptime": 120,
  "messages_sent": 15,
  "messages_received": 8,
  "reconnects": 0,
  "queue_size": 0,
  "timestamp": 1719264035.123,
  "source": "A"
}
```

## Subscription Patterns

### Team A Subscriptions
- `ready` - Monitor Team B readiness
- `start` - Receive game start commands
- `reset` - Handle game resets
- `stage1_attempt` - Receive code attempts from Team B
- `stage1_complete` - Stage 1 completion notifications
- `stage2_current` - Weight updates during Stage 2
- `admin_command` - Admin control commands
- `message_to_a` - Inter-team communication

### Team B Subscriptions
- `ready` - Monitor Team A readiness
- `start` - Receive game start notifications
- `reset` - Handle game resets
- `stage1_code` - Receive codes from Team A
- `stage1_result` - Receive attempt feedback
- `stage2_target` - Receive weight targets
- `admin_command` - Admin control commands
- `message_to_b` - Inter-team communication

## Connection Management

### Auto-Reconnection
- Maximum retry attempts: 5
- Retry delay: 5 seconds with exponential backoff
- Connection health monitoring every 30 seconds
- Automatic resubscription on reconnect

### Message Queuing
- Messages queued during disconnections
- Automatic delivery on reconnection
- Queue size monitoring in heartbeat messages

### Error Handling
- Connection loss detection
- Graceful degradation during network issues
- Comprehensive error logging
- Automatic recovery mechanisms

## Performance Characteristics

### Message Rates
- Heartbeat: Every 30 seconds
- Status reports: Every 60 seconds
- Weight updates: Real-time during Stage 2
- Communication messages: Rate-limited

### Quality of Service
- QoS Level 1: At-least-once delivery
- Retained messages for status topics
- Last will and testament for offline detection

## Integration Points

### Node-RED Dashboard
- Subscribes to all game topics
- Provides admin control interface
- Message history tracking
- Real-time game monitoring

### Admin Controls
- Game start/stop/reset commands
- Team status monitoring
- Performance metrics collection
- Debug message inspection
