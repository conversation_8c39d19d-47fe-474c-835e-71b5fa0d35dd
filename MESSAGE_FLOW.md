# Escape Room Message Flow

## Simple Data Flow Between ESP32 Controllers

This diagram shows the core message exchanges between Team A and Team B ESP32 controllers during gameplay.

```mermaid
sequenceDiagram
    participant A as 🎮 Team A ESP32
    participant MQTT as 🔗 MQTT Broker
    participant B as 🎮 Team B ESP32
    participant Admin as 🎛️ Admin Dashboard

    Note over A,B: Game Initialization
    A->>MQTT: ready {team: A, calibrated: true}
    B->>MQTT: ready {team: B, switches_zeroed: true}
    
    Note over A,B: Game Start
    A->>MQTT: start {game_id: "escape_room_2024"}
    MQTT->>B: start (game begins)
    
    Note over A,B: Stage 1 - Code Challenge
    A->>A: Generate random code (e.g., 456)
    A->>MQTT: stage1_code {code: 456}
    MQTT->>B: stage1_code {code: 456}
    B->>B: Display "Enter Code" on LCD
    
    Note over B: Team B enters code using rotary switches
    B->>B: Rotary input: 4-5-6
    B->>B: Press confirm button
    B->>MQTT: stage1_attempt {code: 456, team: B}
    MQTT->>A: stage1_attempt {code: 456, team: B}
    
    A->>A: Validate code (456 == 456 ✓)
    A->>MQTT: stage1_result {success: true, code: 456}
    MQTT->>B: stage1_result {success: true}
    B->>B: Display "CORRECT!" on LCD
    
    A->>MQTT: stage1_complete {complete: true}
    MQTT->>B: stage1_complete
    
    Note over A,B: Stage 2 - Weight Challenge
    B->>B: Generate random weight (e.g., 5000g)
    B->>MQTT: stage2_target {weight: 5000}
    MQTT->>A: stage2_target {weight: 5000}
    A->>A: Display "Apply 5.0kg" instruction
    
    Note over A: Team A applies pressure
    A->>A: Read pressure sensor (4950g)
    A->>MQTT: stage2_current {weight: 4950, target: 5000}
    MQTT->>B: stage2_current {weight: 4950}
    
    A->>A: Check tolerance (|4950-5000| = 50g ≤ 100g ✓)
    A->>MQTT: stage2_complete {complete: true, final_weight: 4950}
    MQTT->>B: stage2_complete
    B->>B: Display "GAME WON!" on LCD
    
    Note over A,B: 2-Character Communication (via Admin)
    Admin->>MQTT: message_to_b {message: "HI", from_team: A}
    MQTT->>B: message_to_b {message: "HI"}
    B->>B: Display notification + buzzer
    
    Admin->>MQTT: message_to_a {message: "OK", from_team: B}
    MQTT->>A: message_to_a {message: "OK"}
    A->>A: Display notification + buzzer
    
    Note over A,B: Status Monitoring
    A->>MQTT: heartbeat {status: "online", team: A}
    B->>MQTT: heartbeat {status: "online", team: B}
    A->>MQTT: status_team_a {pressure: 4950, stage: 2}
    B->>MQTT: status_team_b {current_code: 456, stage: 2}
```

## Key Message Types

### 1. Game Control Messages
```
Topic: game/{game_id}/control/ready
Payload: {"team": "A", "ready": true, "calibrated": true}

Topic: game/{game_id}/control/start  
Payload: {"game_id": "escape_room_2024", "timestamp": 1719264000}
```

### 2. Stage 1 Messages (Code Challenge)
```
Topic: game/{game_id}/stage1/code_generated
Payload: {"code": 456, "stage": 1, "timestamp": 1719264010}

Topic: game/{game_id}/stage1/code_attempt
Payload: {"code": 456, "team": "B", "attempt_number": 1}

Topic: game/{game_id}/stage1/attempt_result
Payload: {"success": true, "code": 456, "timestamp": 1719264020}
```

### 3. Stage 2 Messages (Weight Challenge)
```
Topic: game/{game_id}/stage2/weight_target
Payload: {"target_weight": 5000, "stage": 2, "team": "B"}

Topic: game/{game_id}/stage2/weight_current
Payload: {"weight": 4950, "target": 5000, "progress": 99.0}

Topic: game/{game_id}/stage2/complete
Payload: {"complete": true, "final_weight": 4950, "tolerance_met": true}
```

### 4. Communication Messages (2-Character Limit)
```
Topic: game/{game_id}/communication/message/A
Payload: {"message": "HI", "from_team": "B", "timestamp": 1719264030}

Topic: game/{game_id}/communication/message/B  
Payload: {"message": "OK", "from_team": "A", "timestamp": 1719264040}
```

### 5. Status Messages
```
Topic: game/{game_id}/heartbeat/A
Payload: {"status": "online", "uptime": 120, "messages_sent": 5}

Topic: game/{game_id}/status/team_a
Payload: {"pressure": 4950, "stage": 2, "calibrated": true}
```

## Message Flow Summary

1. **Initialization**: Both teams report ready status
2. **Game Start**: Team A initiates game and generates code
3. **Code Display**: Team A shows code, Team B attempts entry
4. **Code Validation**: Team A validates and responds with result
5. **Weight Challenge**: Team B shows target, Team A applies pressure
6. **Weight Monitoring**: Real-time pressure updates until target met
7. **Game Complete**: Success celebration and final statistics
8. **Communication**: 2-character messages via admin dashboard throughout

## ESP32 Response Actions

### Team A Responses
- **stage1_attempt** → Validate code → Send result
- **message_to_a** → Display notification + buzzer
- **admin commands** → Execute calibration/reset

### Team B Responses  
- **stage1_code** → Display "Enter Code" prompt
- **stage1_result** → Show success/failure feedback
- **stage2_target** → Display target weight
- **message_to_b** → Display notification + buzzer